import { Button, Text, TextInput, View } from '@components/native';
import { ScreenWrapper } from '@components/shared';
import { Switch } from '@components/shared/animated';
import { Controller, useForm } from 'react-hook-form';
import { Checkbox } from './components/Checkbox';

export const CreatePoll = () => {
  const { control, setValue, getValues } = useForm({
    defaultValues: {
      question: '',
      options: 2,
      option1: '',
      option2: '',
      option3: '',
      option4: '',
      option5: '',
      multiselect: false,
      disableComments: false,
    },
  });

  return (
    <ScreenWrapper
      title={
        <Text ff="PlayfairDisplay-SemiBold" fs="20" fw="600">
          New Poll
        </Text>
      }>
      <View px={20} pt={16} flex={1} jc="space-between">
        <View>
          <Text fw="400" fs="12" color="purple500" ml={9} mb={4}>
            Question
          </Text>
          {/* Add minLength 30 */}
          <TextInput label="Ask a question" labelType="background" maxLength={1000} control={control} name="question" gapBottom={4} />
          <Text fw="400" fs="10" color="neutral50" ml={9} mb={24}>
            25 maximum characters
          </Text>
          <View fd="row" ai="center" jc="space-between" ml={9} mb={4}>
            <Text fw="400" fs="12" color="purple500">
              Options
            </Text>
            <View fd="row" ai="center">
              <Controller
                control={control}
                name="multiselect"
                render={({ field: { value, onChange } }) => (
                  <Checkbox
                    checked={value}
                    onChange={newValue => {
                      console.log('newValue', newValue);
                      onChange(newValue);
                      setValue('multiselect', newValue);
                    }}
                  />
                )}
              />
              <Text fw="400" fs="12" color="neutral70" ml={4}>
                Multiselect
              </Text>
            </View>
          </View>
          <Controller
            control={control}
            name="options"
            render={({ field: { value, onChange } }) => (
              <>
                {[...Array(value)].map((_, index) => (
                  //  Add minLength 2
                  <TextInput key={index} label={`Option ${index + 1}`} labelType="background" maxLength={50} control={control} name={`option${index + 1}`} gapBottom={12} />
                ))}
                {value < 5 && (
                  <Button bw={1} bc="purple500" bg="transparent" color="purple700" isFullWidth={true} onPress={() => onChange(value + 1)} disabled={value >= 5}>
                    + ADD OPTION
                  </Button>
                )}
              </>
            )}
          />
          <View fd="row" ai="center" jc="space-between" pt={41}>
            <Text fw="400" fs="12" color="neutral70">
              Disable Comments
            </Text>
            <Controller
              control={control}
              name="disableComments"
              render={({ field: { value, onChange } }) => (
                <Switch
                  isActive={value}
                  onToggle={newValue => {
                    onChange(newValue);
                    setValue('disableComments', newValue);
                  }}
                />
              )}
            />
          </View>
        </View>
        <Button mb={53} isFullWidth={true} onPress={() => {}}>
          ADD
        </Button>
      </View>
    </ScreenWrapper>
  );
};
