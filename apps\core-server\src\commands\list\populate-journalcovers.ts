import { Command } from "@/lib/command-module.js";
import { suggestUniqueUsername } from "@/routes/user/lib.js";
import db from "@repo/db";
import {
  journalCategoryTable,
  journalCoversTable,
  moodTable,
  notificationsTable,
} from "@repo/db/schema";

import { logger, pathGenerator, S3Bucket } from "@repo/lib";
import { JournalRepo } from "@repo/repo";
import dayjs from "dayjs";
import { sql } from "drizzle-orm";
import path from "path";

const MOCK_JOURNAL_COVERS = [
  {
    name: "All",
    url: S3Bucket.getAwsUrl(
      pathGenerator.journalCategories + "/all-journal-bg.png",
    ),
  },
  {
    name: "Gratitude",
    url: S3Bucket.getAwsUrl(
      pathGenerator.journalCategories + "/gratitude-journal-bg.png",
    ),
  },
  {
    name: "Lost",
    url: S3Bucket.getAwsUrl(
      pathGenerator.journalCategories + "/lost-journal-bg.png",
    ),
  },
  {
    name: "Anger",
    url: S3Bucket.getAwsUrl(
      pathGenerator.journalCategories + "/anger-journal-bg.png",
    ),
  },
  {
    name: "Love",
    url: S3Bucket.getAwsUrl(
      pathGenerator.journalCategories + "/love-journal-bg.png",
    ),
  },
  {
    name: "Anxiety",
    url: S3Bucket.getAwsUrl(
      pathGenerator.journalCategories + "/anxiety-journal-bg.png",
    ),
  },
];

export const populateJournalCovers = new Command({
  name: "populate-journal-covers",
  description: "add journal cover images",
  fn: async () => {
    logger.info("Task Started");
    // await S3Bucket.uploadDirectory(
    //   path.join(process.cwd(), "src/storage/journal-categories"),
    //   "journal-covers",
    // );

    // await db.insert(journalCoversTable).values(MOCK_JOURNAL_TYPES);
    await JournalRepo.addJournalCover({
      tx: db,
      data: MOCK_JOURNAL_COVERS,
    });
    logger.info("Task Done");
  },
});
