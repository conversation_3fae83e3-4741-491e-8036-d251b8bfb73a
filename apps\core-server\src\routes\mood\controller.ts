import { Infer } from "@vinejs/vine/types";
import type { Request, Response, NextFunction } from "express";
import { getMoodValidator, logMoodValidator } from "./validator.js";
import { bodyValidator, queryValidator } from "@/lib/validation-module.js";
import db from "@repo/db";
import { moodTable } from "@repo/db/schema";
import { and, eq, gte, lte, sql } from "drizzle-orm";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc.js";
dayjs.extend(utc);

export class MoodController {
  @bodyValidator(logMoodValidator)
  static async logMood(req: Request, res: Response, next: NextFunction) {
    try {
      const payload = req.body as Infer<typeof logMoodValidator>;
      await db.insert(moodTable).values({
        value: payload.mood,
        userId: req.user.id,
        timestamp: dayjs(payload.timestamp).utc().toDate(),
      });
      res.json({ message: "Mood logged", success: true });
      return;
    } catch (error) {
      next(error);
    }
  }
  @queryValidator(getMoodValidator)
  static async getWeekMoodLog(req: Request, res: Response, next: NextFunction) {
    try {
      const date = (req.query.from as string) || dayjs().toISOString();
      const targetDate = dayjs(date);
      const lastWeek = targetDate.subtract(6, "day");

      console.log(targetDate.toDate());

      const { rows } = (await db.execute(sql`
            SELECT TO_CHAR(${moodTable.timestamp}, 'Dy') AS weekday, 
            JSON_AGG(
              JSON_BUILD_OBJECT(
                'value', ${moodTable.value},
                'timestamp', ${moodTable.timestamp}
                ) ORDER BY ${moodTable.timestamp} ASC) as logs  
            FROM ${moodTable}
            WHERE  ${moodTable.timestamp} <= ${targetDate.endOf("day").toISOString()}
            AND  ${moodTable.timestamp} >=  ${lastWeek.startOf("day").toISOString()}
            AND ${moodTable.userId} = ${req.user.id}
            GROUP BY TO_CHAR(${moodTable.timestamp}, 'Dy')
            ORDER BY MAX(${moodTable.timestamp}) DESC;
          `)) satisfies {
        rows: { weekday: string; logs: { value: string; timestamp: Date }[] }[];
      };
      console.log({ rows });
      res.json({ data: rows });
    } catch (error) {
      next(error);
    }
  }
  static async getCalender(req: Request, res: Response, next: NextFunction) {
    try {
      // TODO: get the last log of the day for
      // const { rows } = await db.execute(sql`
      //    SELECT value, TO_CHAR(timestamp, 'MM-DD-YYYY') AS date
      //   FROM (
      //     SELECT value, timestamp, ROW_NUMBER() OVER (PARTITION BY TO_CHAR(timestamp, 'M-DD-YYYYM') ORDER BY timestamp DESC) AS rn
      //     FROM mood_table
      //   ) sub
      //   WHERE rn = 1
      //   `);

      const sq = db.$with("sq").as(
        db
          .select({
            user_id: moodTable.userId,
            value: moodTable.value,
            timestamp: moodTable.timestamp,
            row_number: sql<number>`ROW_NUMBER() OVER (
            PARTITION BY TO_CHAR(${moodTable.timestamp}, 'MM-DD-YYYY')
            ORDER BY ${moodTable.timestamp} DESC 
            ) as row_number`,
          })
          .from(moodTable),
      );

      const rows = await db
        .with(sq)
        .select({
          value: sql<string>`value`,
          date: sql<string>`TO_CHAR(timestamp, 'MM-DD-YYYY')`,
        })
        .from(sq)
        .where(and(sql`row_number = 1`, sql`user_id = ${req.user.id}`));

      res.json({ data: rows });
    } catch (error) {
      next(error);
    }
  }
}
