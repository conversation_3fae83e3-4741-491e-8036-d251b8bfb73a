import { Command } from "@/lib/command-module.js";
import { suggestUniqueUsername } from "@/routes/user/lib.js";
import db from "@repo/db";
import { moodTable, notificationsTable } from "@repo/db/schema";

import { logger } from "@repo/lib";
import dayjs from "dayjs";
import { sql } from "drizzle-orm";

const userID = process.env.TEST_USER_ID;

export const populateLogs = new Command({
  name: "populate-logs",
  description: "Add a bunch of logs for testing",
  fn: async () => {
    logger.info("Task Started");
    await seedLogs(300);
    logger.info("Task Done");
  },
});
function getRandomTimestamp(): Date {
  const now = new Date();
  const past30 = new Date(now);
  past30.setDate(now.getDate() - 30);

  const randomTime = new Date(
    past30.getTime() + Math.random() * (now.getTime() - past30.getTime()),
  );
  return randomTime;
}

const moods = ["Heartbroken", "Frustrated", "Anxious", "Optimistic", "Healing"];

async function seedLogs(count: number = 300) {
  const entries = Array.from({ length: count }).map(() => ({
    value: moods[Math.floor(Math.random() * moods.length)]!,
    timestamp: getRandomTimestamp(),
    userId: userID,
  }));

  await db.insert(moodTable).values(entries);
  console.log(`Seeded ${count} log entries.`);
}
