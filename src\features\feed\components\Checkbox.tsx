import { useTheme } from '@context/index';
import React from 'react';
import { Pressable, StyleProp, StyleSheet, ViewStyle } from 'react-native';
import Animated, { useAnimatedStyle, useSharedValue, withTiming } from 'react-native-reanimated';
import Feather from 'react-native-vector-icons/Feather';

export interface CheckboxProps {
  checked: boolean;
  onChange: (checked: boolean) => void;
  size?: number;
  disabled?: boolean;
  style?: StyleProp<ViewStyle>;
}

export const Checkbox: React.FC<CheckboxProps> = ({ checked, onChange, size = 14, disabled = false, style }) => {
  const { colors } = useTheme();
  const animatedValue = useSharedValue(checked ? 1 : 0);

  React.useEffect(() => {
    animatedValue.value = withTiming(checked ? 1 : 0, { duration: 200 });
  }, [checked]);

  const boxStyle = {
    ...styles.container,
    width: size,
    height: size,
    borderRadius: 4,
    borderWidth: 1,
    borderColor: colors.purple500,
    backgroundColor: checked ? colors.purple500 : 'transparent',
    opacity: disabled ? 0.5 : 1,
  };

  const checkAnimatedStyle = useAnimatedStyle(() => ({
    opacity: animatedValue.value,
    transform: [{ scale: animatedValue.value }],
  }));

  return (
    <Pressable
      onPress={() => !disabled && onChange(!checked)}
      disabled={disabled}
      style={({ pressed }) => [boxStyle, style, pressed && !disabled ? { opacity: 0.7 } : {}]}
      accessibilityRole="checkbox"
      accessibilityState={{ checked, disabled }}>
      <Animated.View style={[styles.check, checkAnimatedStyle]}>
        <Feather name="check" size={size * 0.7} color="white" />
      </Animated.View>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  check: {
    position: 'absolute',
    left: 0,
    top: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
