import FeatherIcon from '@assets/svgs/feather-icon.svg';
import PlusIcon from '@assets/svgs/plus-icon.svg';
import {Pressable, Text, View} from '@components/native';
import {AnimatedView, BounceTap, Ripple} from '@components/shared/animated';
import {useTheme} from '@context/index';
import {router} from '@navigation/refs/navigation-ref';
import {FlashList} from '@shopify/flash-list';
import {format, parseISO} from 'date-fns';
import React, {useRef, useState} from 'react';
import {Dimensions, FlatList, Image, ImageSourcePropType} from 'react-native';
import Animated, {Extrapolation, interpolate, runOnJS, SharedValue, useAnimatedReaction, useAnimatedScrollHandler, useAnimatedStyle, useSharedValue} from 'react-native-reanimated';
import {SceneMap, TabBar, TabView} from 'react-native-tab-view';
const {width} = Dimensions.get('screen');

interface MockJournalType {
  id: string;
  type: string;
  title: string;
  totalEntries: number;
  bgImage?: ImageSourcePropType;
}

const MOCK_JOURNAL_TYPES: MockJournalType[] = [
  {id: '0', type: 'all', title: 'All', totalEntries: 2, bgImage: require('@assets/images/all-journal-bg.png')},
  {id: '1', type: 'gratitude', title: 'Gratitude', totalEntries: 20, bgImage: require('@assets/images/gratitude-journal-bg.png')},
  {id: '2', type: 'lost', title: 'Lost', totalEntries: 20, bgImage: require('@assets/images/lost-journal-bg.png')},
  {id: '3', type: 'anger', title: 'Anger', totalEntries: 18, bgImage: require('@assets/images/anger-journal-bg.png')},
  {id: '4', type: 'love', title: 'Love', totalEntries: 10, bgImage: require('@assets/images/love-journal-bg.png')},
  {id: '5', type: 'anxiety', title: 'Anxiety', totalEntries: 8, bgImage: require('@assets/images/anxiety-journal-bg.png')},
];

type MockJournalItem = {
  id: string;
  title: string;
  description: string;
  type: string;
  bgImage: string;
  createdAt: string;
};
const MOCK_JOURNAL_ITEMS: MockJournalItem[] = [
  {
    id: '1',
    title: '5 Things i am grateful of .. Things i am grateful of .... Things i am grateful of ....Things i am grateful of ',
    description: 'This is a teste description of the markdown format ?///// ',
    bgImage: '',
    createdAt: '2025-06-25T00:00:00',
    type: 'gratitude',
  },
] as const;

interface TabRoute {
  key: string;
  title: string;
}

// Move renderItem to a separate component to avoid hook issues
const CarouselItem: React.FC<{
  item: MockJournalType;
  index: number;
  scrollX: SharedValue<number>;
  cardWidth: number;
  spacing: number;
}> = ({item, index, scrollX, cardWidth, spacing}) => {
  const animatedStyle = useAnimatedStyle(() => {
    const inputRange = [(index - 1) * (cardWidth + spacing), index * (cardWidth + spacing), (index + 1) * (cardWidth + spacing)];
    return {
      transform: [
        {
          scale: interpolate(scrollX.value, inputRange, [0.9, 1.1, 0.9], Extrapolation.CLAMP),
        },
      ],
      width: cardWidth + 1.2,
      marginHorizontal: spacing / 2,
    };
  });

  return (
    <AnimatedView style={[animatedStyle]}>
      <Image
        style={{
          position: 'absolute',
          objectFit: 'cover',
          top: 0,
          left: 0,
          height: '100%',
          width: '100%',
          borderTopRightRadius: 20,
          borderBottomRightRadius: 20,
        }}
        source={item.bgImage}
      />
      <View
        style={{
          height: cardWidth * 1.3,
        }}>
        <Text ml={30} mt={30} fw="600" fs="18" ff="PlayfairDisplay-SemiBold" color="black">
          {item.title}
        </Text>
        <View
          bg="purple100"
          bc="purple500"
          bw={1}
          br={100}
          display="flex"
          fd="row"
          ai="center"
          gap={5}
          px={5}
          py={4}
          style={{
            marginLeft: 30,
            marginTop: 5,
            alignSelf: 'flex-start',
          }}>
          <FeatherIcon />
          <Text color="purple700" fs="10">
            {item.totalEntries} Entries
          </Text>
        </View>
      </View>
    </AnimatedView>
  );
};

// Create a scene for each tab
const renderScene = SceneMap(
  MOCK_JOURNAL_TYPES.reduce(
    (scenes, item, index) => ({
      ...scenes,
      [item.id]: () => (
        <FlashList
          ItemSeparatorComponent={() => <View style={{height: 10}} />}
          data={Array.from({length: 200}).fill(MOCK_JOURNAL_ITEMS[0])}
          contentContainerStyle={{padding: 10}}
          renderItem={({item, index}) => {
            const obj = item as MockJournalItem;
            const date = parseISO(obj.createdAt + 'Z'); // Parse UTC string
            const formattedDate = format(date, 'dd/MMM , yyyy').split('/');

            return (
              <Pressable
                onPress={() => {
                  router.navigate('JournalDetails', {journalId: '1234', type: 'Gratitude Journal', publishedOn: formattedDate[0] + formattedDate[1]});
                }}
                key={index}>
                <View p={10}>
                  <View display="flex" ai="center" fd="row">
                    <Text fw="600" fs="16">
                      {formattedDate[0]}{' '}
                    </Text>
                    <Text fs="12">{formattedDate[1]}</Text>
                  </View>
                  <View display="flex" fd="row" ai="center" gap={5}>
                    <View size={60} mt={10} bg="negative70" br={10} />
                    <View p={8} disableSizeMatter w={width - 100}>
                      <Text color="neutral80" fw="500" numberOfLines={1}>
                        {obj.title}
                      </Text>
                      <Text mt={5} fs="14">
                        {obj.description}
                      </Text>
                    </View>
                  </View>
                </View>
              </Pressable>
            );
          }}
          estimatedItemSize={110}
        />
      ),
    }),
    {} as Record<string, React.FC>,
  ),
);

const CustomTabBar = ({...props}: any) => {
  const {colors} = useTheme();
  return (
    <TabBar
      bounces={false}
      tabStyle={{width: 'auto', minWidth: width / MOCK_JOURNAL_TYPES.length, paddingHorizontal: 10}}
      indicatorContainerStyle={{minWidth: width, backgroundColor: colors.background, borderTopLeftRadius: 20, borderTopRightRadius: 20}}
      activeColor={colors.neutral80}
      inactiveColor={colors.neutral60}
      scrollEnabled={true}
      {...props}
      indicatorStyle={{
        height: 4,
        backgroundColor: colors.purple500,
      }}
      style={{
        backgroundColor: colors.orange,
        borderBottomColor: colors.neutral20,
        borderBottomWidth: 1,
        borderTopLeftRadius: 20,
        borderTopRightRadius: 20,
      }}
    />
  );
};

export const JournalScreen: React.FC = () => {
  const {colors, isDarkMode} = useTheme();
  const layout = Dimensions.get('window');
  const [index, setIndex] = useState(0);
  const [routes] = useState<TabRoute[]>(MOCK_JOURNAL_TYPES.map(item => ({key: item.id, title: item.title})));
  const flatListRef = useRef<FlatList>(null);
  const scrollX = useSharedValue(0);
  const isTransitionOngoing = useSharedValue(false);

  const CARD_WIDTH = layout.width * 0.45;
  const SPACING = 25;
  const SIDE_SPACER = (layout.width - CARD_WIDTH) / 2;

  // Sync carousel scroll with TabView index
  useAnimatedReaction(
    () => ({scrollX: scrollX.value, isTransitionOngoing: isTransitionOngoing.value}),
    ({scrollX, isTransitionOngoing}) => {
      if (isTransitionOngoing) return;
      const newIndex = Math.round(scrollX / (CARD_WIDTH + SPACING));
      if (newIndex !== index && newIndex >= 0 && newIndex < MOCK_JOURNAL_TYPES.length) {
        runOnJS(setIndex)(newIndex);
      }
    },
    [index, CARD_WIDTH, SPACING],
  );

  // Handle TabView index change to scroll carousel
  const handleIndexChange = (newIndex: number) => {
    if (newIndex === index) return;
    isTransitionOngoing.value = true;
    setIndex(newIndex);
    if (flatListRef.current) {
      flatListRef.current.scrollToIndex({
        index: newIndex,
        animated: Math.abs(newIndex - index) <= 2,
      });
    }
    // Dynamic timeout based on index jump distance
    const jumpDistance = Math.abs(newIndex - index);
    const timeoutDuration = Math.max(300, jumpDistance * 50); // 50ms per index
    setTimeout(() => {
      isTransitionOngoing.value = false;
    }, timeoutDuration);
  };

  const scrollHandler = useAnimatedScrollHandler({
    onScroll: event => {
      scrollX.value = event.contentOffset.x;
    },
    onBeginDrag: () => {
      isTransitionOngoing.value = true;
    },
    onMomentumEnd: () => {
      isTransitionOngoing.value = false;
    },
  });

  return (
    <View flex={1} style={{backgroundColor: isDarkMode ? '#081012' : colors.orange}}>
      <View pos="absolute" bottom={60} z={20} right={24}>
        <BounceTap onPress={() => router.push('AddJournal')}>
          <View size={50} flexCenterRow bg="purple500" br={100} bw={1} bc="purpleLight">
            <PlusIcon fill="white" />
          </View>
        </BounceTap>
      </View>
      <View style={{backgroundColor: isDarkMode ? '#081012' : colors.orange}} pb={25}>
        <View style={{padding: 20}}>
          <Ripple onPress={() => router.navigate('JournalSearch')}>
            <View bg="white" bc="neutral20" bw={1} br={16} px={20} py={14}>
              <Text color="neutral40" fs="14">
                Search
              </Text>
            </View>
          </Ripple>
        </View>
        <Animated.FlatList
          ListHeaderComponent={() => {
            return <></>;
          }}
          ref={flatListRef}
          pagingEnabled
          data={MOCK_JOURNAL_TYPES}
          renderItem={({item, index}) => <CarouselItem item={item} index={index} scrollX={scrollX} cardWidth={CARD_WIDTH} spacing={SPACING} />}
          keyExtractor={item => item.id}
          horizontal
          showsHorizontalScrollIndicator={false}
          snapToInterval={CARD_WIDTH + SPACING}
          decelerationRate={-1}
          bounces={false}
          contentContainerStyle={{paddingHorizontal: SIDE_SPACER}}
          onScroll={scrollHandler}
          scrollEventThrottle={16}
          getItemLayout={(data, index) => ({
            length: CARD_WIDTH + SPACING,
            offset: (CARD_WIDTH + SPACING) * index,
            index,
          })}
        />
      </View>
      <TabView
        navigationState={{index, routes}}
        renderScene={renderScene}
        onIndexChange={handleIndexChange}
        initialLayout={{width: layout.width}}
        style={{backgroundColor: colors.background, borderRadius: 20}}
        renderTabBar={CustomTabBar}
      />
    </View>
  );
};
