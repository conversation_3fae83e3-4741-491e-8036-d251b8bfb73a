import db, { Dr<PERSON>zleDB, DrizzleTransaction } from "@repo/db";
import { avatarTable, filesTable, postFilesTable, userDetails, users } from "@repo/db/schema";
import { eq, inArray, and } from "drizzle-orm";
import { defaultAvatarUrl, pathGenerator, S3Bucket, UploadFile } from "@repo/lib";
import { ImageCompressionModule } from "./image-module/compression-module.js";

/**
 * @description Wrapper around s3 storage bucket for most use cases
 */
export class MediaModule {
  // set profile image, and remove if last one was found

  static async uploadFile(file: UploadFile, subpath: string) {
    // const compressedFile =
    //   await ImageCompressionModule.generalCompression(file);
    const blurHash = await ImageCompressionModule.generateBlurHash(file);
    const url = await S3Bucket.uploadFile(file, subpath);
    const [mediaFile] = await db
      .insert(filesTable)
      .values([
        {
          fileName: file.name,
          mimetype: file.mimetype,
          url: url,
          preview: blurHash
        }
      ])
      .returning();
    return mediaFile;
  }

  static async uploadManyImages(files: UploadFile[], subpath: string, generatePreview = true) {
    const medium = await Promise.all(
      files.map(async (file) => ImageCompressionModule.getCompressedImage(file, "medium"))
    );
    const uploadedMediumImages = await S3Bucket.uploadFiles({
      files: medium,
      path: subpath
    });

    let uploadedPreviewImages = uploadedMediumImages;
    if (generatePreview) {
      const preview = await Promise.all(
        files.map(async (file) => ImageCompressionModule.getCompressedImage(file, "preview"))
      );
      uploadedPreviewImages = await S3Bucket.uploadFiles({
        files: preview,
        path: `${subpath}/preview`
      });
    }

    return db
      .insert(filesTable)
      .values(
        files.map((f, i) => ({
          fileName: f.name,
          mimetype: f.mimetype,
          url: uploadedMediumImages[i]!.url!,
          preview: uploadedPreviewImages[i]?.url!
        }))
      )
      .returning();
  }

  static async uploadJournalImages(userID: string, files: UploadFile[]) {
    if (files.length == 0) return [];
    const path = pathGenerator["journal"]["images"](userID);
    return MediaModule.uploadManyImages(files, path, false);
  }

  static async uploadJournalAudio(userID: string, files: UploadFile[]) {
    if (files.length == 0) return [];
    const path = pathGenerator["journal"]["images"](userID);
    const uploadedAudio = await S3Bucket.uploadFiles({
      files: files,
      path: path
    });
    return db
      .insert(filesTable)
      .values(
        files.map((f, i) => ({
          fileName: f.name,
          mimetype: f.mimetype,
          url: uploadedAudio[i]!.url!,
          preview: ""
        }))
      )
      .returning();
  }

  static async uploadPostsMedia(userID: string, files: UploadFile[], type: "posts") {
    const path = pathGenerator[type](userID);
    return MediaModule.uploadManyImages(files, path);
  }

  static async deletePostMedia(mediaIds: string[], tx: DrizzleTransaction | DrizzleDB = db) {
    const files = await db.query.filesTable.findMany({
      where: (data, { inArray }) => inArray(data.id, mediaIds)
    });
    await tx.delete(filesTable).where(inArray(filesTable.id, mediaIds)).returning();
    const deleted = await Promise.all(
      files.map(async (file) => {
        const path = S3Bucket.getKeyFromUrl(file.url);
        if (!path || path.startsWith(pathGenerator.asset.base)) return false;
        await S3Bucket.deleteFile(file.url);
        return true;
      })
    );
    return deleted.every((d) => d == false);
  }
  // static async setHostingMedia() {}
  // static async deleteHostingMedia() {}
  // static async setProductMedia() {}
  // static async deleteProductMedia() {}
  // static async setServiceMedia() {}
  // static async deleteServiceMedia() {}
}
