import MoodHeart from '@assets/svgs/mood-heart.svg';
import { <PERSON>ton, SafeAreaView, Text, View } from '@components/native';
import { notify, useTheme } from '@context/index';
import { Slider } from '@miblanchard/react-native-slider';
import { router } from '@navigation/refs/navigation-ref';
import useStitch from '@packages/useStitch';
import { FlashList } from '@shopify/flash-list';
import SCREENS from '@src/SCREENS';
import React, { useState } from 'react';
import { Dimensions, Image, ImageSourcePropType, Pressable } from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import LinearGradient from 'react-native-linear-gradient';
import Svg, { Circle, G, Line, Text as SvgText, Path } from 'react-native-svg';
import Feather from 'react-native-vector-icons/Feather';
import Animated, { useSharedValue, useAnimatedProps, withSpring, interpolateColor } from 'react-native-reanimated';
import { MotiView } from 'moti';

interface Mood {
  name: string;
  description: string;
  bgColor: string;
  heartColor: string;
}

const moods: Mood[] = [
  {
    name: 'Heartbroken',
    description: 'This chapter is heavy,\nbut your story isn’t over. 📖',
    bgColor: '#C7C3E5',
    heartColor: '#595387',
  },
  {
    name: 'Frustrated',
    description: "You're allowed to feel this way.\nLet’s help you move through it. 💫",
    bgColor: '#FEDABB',
    heartColor: '#FFA04C',
  },
  {
    name: 'Anxious',
    description: 'It’s okay to feel this way. \nYou’re not alone. 🤍',
    bgColor: '#CAD9FA',
    heartColor: '#828CFF',
  },
  {
    name: 'Optimistic',
    description: 'That’s wonderful! \nKeep the good vibes going! ✨',
    bgColor: '#FAF6CA',
    heartColor: '#FFFB8C',
  },
  {
    name: 'Healing',
    description: 'You’re learning to bloom again. \nKeep going. 🌸',
    bgColor: '#FDDEDE',
    heartColor: '#FFB7B7',
  },
];


const AnimatedMoodHeart = Animated.createAnimatedComponent(MoodHeart);

const MoodHeartWithAnimation: React.FC<{ color: string; previousColor?: string }> = ({ color, previousColor }) => {
  const colorProgress = useSharedValue(1);

  React.useEffect(() => {
    if (previousColor && previousColor !== color) {
      colorProgress.value = 0;
      colorProgress.value = withSpring(1, {
        damping: 20,
        stiffness: 300,
      });
    }
  }, [color, previousColor, colorProgress]);

  const animatedProps = useAnimatedProps(() => {
    if (!previousColor || previousColor === color) {
      return { color };
    }

    const animatedColor = interpolateColor(colorProgress.value, [0, 1], [previousColor, color]);

    return { color: animatedColor };
  });

  return <AnimatedMoodHeart animatedProps={animatedProps} />;
};

const AnimatedBackgroundGradient: React.FC<{
  bgColor: string;
  baseColor: string;
  style: any;
}> = ({ bgColor, baseColor, style }) => {
  return (
    <MotiView
      animate={{ backgroundColor: bgColor }}
      transition={{
        type: 'spring',
        damping: 20,
        stiffness: 300,
      }}
      style={style}>
      <LinearGradient
        colors={[baseColor, 'transparent']}
        style={{
          position: 'absolute',
          left: 0,
          right: 0,
          top: 0,
          bottom: 0,
        }}
      />
    </MotiView>
  );
};

const MoodHeartSlider: React.FC = React.memo(() => {
  const { colors } = useTheme();
  const [sliderValue, setSliderValue] = useState(0);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [reloadKey, setReloadKey] = useState(0);
  const [previousColor, setPreviousColor] = useState<string>(moods[0].heartColor);

  React.useEffect(() => {
    const rounded = Math.max(0, Math.min(moods.length - 1, Math.round(sliderValue)));
    if (rounded !== selectedIndex) {
      setPreviousColor(moods[selectedIndex].heartColor);
      setSelectedIndex(rounded);
    }
  }, [sliderValue, selectedIndex]);

  const { isMutating, mutateAsync } = useStitch('logMood', {
    mutationOptions: {
      onSuccess: () => {
        notify.bottom('Mood logged!');
        setReloadKey(k => k + 1);
      },
      onError: error => console.error('Log mood error:', error),
    },
  });

  return (
    <View h={730}>
      <AnimatedBackgroundGradient
        bgColor={moods[selectedIndex].bgColor}
        baseColor={colors.background}
        style={{
          position: 'absolute',
          left: 0,
          right: 0,
          top: 0,
          height: 531,
          borderBottomLeftRadius: 40,
          borderBottomRightRadius: 40,
        }}
      />
      <View top={60}>
        <Text fs="16" fw="500" color="neutral80" ta="center" mb={42}>
          {moods[selectedIndex].description}
        </Text>
        <Text fs="24" fw="700" color="neutral70" ta="center" mb={42}>
          {moods[selectedIndex].name}
        </Text>
        <View ai="center" mb={20}>
          <MoodHeartWithAnimation color={moods[selectedIndex].heartColor} previousColor={previousColor} />
        </View>
        <View px={25}>
          <Slider
            minimumValue={0}
            maximumValue={moods.length - 1}
            value={sliderValue}
            onValueChange={value => setSliderValue(Array.isArray(value) ? value[0] : value)}
            onSlidingComplete={value => {
              const snapped = Math.round(value[0]);
              setSliderValue(snapped);
              setSelectedIndex(snapped);
            }}
            minimumTrackTintColor={colors.background}
            maximumTrackTintColor={colors.background}
            thumbTintColor={colors.purple500}
            thumbStyle={{
              width: 50,
              height: 50,
              borderRadius: 25,
              shadowColor: '#000',
              shadowOffset: { width: 2, height: 2 },
              shadowOpacity: 0.25,
              shadowRadius: 5,
              elevation: 5,
            }}
            trackStyle={{ height: 32, borderRadius: 16 }}
          />
        </View>
        <View fd="row" jc="space-between" mt={15} pl={15} pr={30}>
          <Text fs="10" fw="400" color="neutral70">
            Heartbroken
          </Text>
          <Text fs="10" fw="400" color="neutral70">
            Frustrated
          </Text>
          <Text fs="10" fw="400" color="neutral70">
            Anxious
          </Text>
          <Text fs="10" fw="400" color="neutral70">
            Optimistic
          </Text>
          <Text fs="10" fw="400" color="neutral70">
            Healing
          </Text>
        </View>
        <Button mt={20} mx={15} isFullWidth={true} isLoading={isMutating} onPress={() => mutateAsync({ mood: moods[selectedIndex].name, timestamp: new Date() })}>
          LOG MOOD
        </Button>
        <MoodChart reloadKey={reloadKey} />
      </View>
    </View>
  );
});

const MoodChart: React.FC<{ reloadKey: number }> = ({ reloadKey }) => {
  const [weekEnd, setWeekEnd] = React.useState(new Date());
  const moodCache = React.useRef<Record<string, any>>({});
  const moodColorMap = React.useMemo(() => Object.fromEntries(moods.map(m => [m.name, m.heartColor])), []);
  const weekDays = React.useMemo(
    () =>
      Array.from({ length: 7 }, (_, i) => {
        const d = new Date(weekEnd);
        d.setDate(weekEnd.getDate() - (6 - i));
        return d;
      }),
    [weekEnd],
  );

  const getWeekEndISO = (date: Date) => new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59, 999)).toISOString();
  const isToday = (date: Date) => {
    const now = new Date();
    return date.getDate() === now.getDate() && date.getMonth() === now.getMonth() && date.getFullYear() === now.getFullYear();
  };
  const formatWeekRange = (days: Date[]) =>
    `${days[0].getDate()} ${days[0].toLocaleString('default', { month: 'short' })} - ${days[6].getDate()} ${days[6].toLocaleString('default', { month: 'short' })}`;

  const { mutateAsync } = useStitch('getMoodWeek', {
    queryParams: { from: weekEnd.toISOString() },
    mutationOptions: {
      onSuccess: data => {
        moodCache.current[getWeekEndISO(weekEnd)] = data;
        console.log('Mood chart [', getWeekEndISO(weekEnd), '] fetched:', data);
      },
      onError: error => console.error('Mood chart error:', error),
    },
  });

  React.useEffect(() => {
    const weekEndISO = getWeekEndISO(weekEnd);
    if (!moodCache.current[weekEndISO] || isToday(weekEnd)) mutateAsync();
  }, [mutateAsync, weekEnd, reloadKey]);

  const weekEndISO = getWeekEndISO(weekEnd);
  const moodChartDataArr = Array.isArray(moodCache.current[weekEndISO]?.data) ? moodCache.current[weekEndISO].data : [];
  const chartData = weekDays.map(day => {
    const entry = moodChartDataArr.find((d: { weekday: string; logs: { value: string; timestamp: string }[] }) => d.weekday === day.toLocaleDateString('en-US', { weekday: 'short' }));
    return (entry?.logs || []).map((log: { value: string; timestamp: string }) => ({
      hour: new Date(log.timestamp).getHours(),
      color: moodColorMap[log.value],
      mood: log.value,
    }));
  });

  const { colors } = useTheme();
  const width = Dimensions.get('window').width;
  const height = 309;
  const padding = 45;
  const chartW = width - padding * 2;
  const chartH = height - padding * 2;
  const dayStep = chartW / 6;
  const timeStep = chartH / 6;
  const yTimes = ['12 am', '4 am', '8 am', '12 pm', '4 pm', '8 pm', ' 12 am'];

  const getY = (hour: number) => {
    const pointSpacing = chartH / 23;
    const roundedHour = Math.round(hour);
    return padding + roundedHour * pointSpacing;
  };

  // Week navigation handlers
  const goPrevWeek = () => {
    setWeekEnd(prev => {
      const newDate = new Date(prev);
      newDate.setDate(prev.getDate() - 7);
      return newDate;
    });
  };
  const goNextWeek = () => {
    setWeekEnd(prev => {
      const next = new Date(prev);
      next.setDate(prev.getDate() + 7);
      // Don't allow going into the future
      const now = new Date();
      return next <= now ? next : prev;
    });
  };

  return (
    <View mt={30} mb={20} px={10}>
      {/* Week Selector */}
      <View fd="row" ai="center" jc="space-between">
        <Pressable onPress={goPrevWeek}>
          <Feather name="chevron-left" size={24} color={colors.neutral70} />
        </Pressable>
        <Text fs="16" fw="600" color="neutral80">
          {formatWeekRange(weekDays)}
        </Text>
        <Pressable onPress={goNextWeek}>
          <Feather name="chevron-right" size={24} color={colors.neutral70} />
        </Pressable>
      </View>
      {/* Chart */}
      <Svg width={width} height={height}>
        {/* Y axis labels */}
        {yTimes.map((label, i) => (
          <SvgText key={label} x={padding - 8} y={padding + i * timeStep + 4} fontFamily="MontserratRegular" fontSize="12" fontWeight={'400'} fill={colors.neutral50} textAnchor="end">
            {label}
          </SvgText>
        ))}
        {/* Vertical dotted lines and day labels */}
        {weekDays.map((d, i) => (
          <G key={i}>
            <Line x1={padding + i * dayStep} y1={padding} x2={padding + i * dayStep} y2={height - padding} stroke={colors.neutral10} strokeDasharray="8 8" strokeWidth={1} />
            <SvgText x={padding + i * dayStep} y={height - padding + 16} fontFamily="MontserratRegular" fontSize="10" fill={colors.neutral60} textAnchor="middle">
              {d.toLocaleDateString('en-US', { weekday: 'short' })}
            </SvgText>
          </G>
        ))}
        {/* Mood dots */}
        {chartData.map((logs, dayIdx) =>
          logs.map((dot: { hour: number; color: string; mood: string }, idx: number) => (
            <Circle key={dayIdx + '-' + idx} cx={padding + dayIdx * dayStep} cy={getY(dot.hour)} r={6} fill={dot.color} stroke="#fff" strokeWidth={2} />
          )),
        )}
      </Svg>
      <View pos="absolute" right={0.01} bottom={0.01} style={{ width: '40%', height: '85%', opacity: 0.7, backgroundColor: colors.background }} />
      <View
        pos="absolute"
        right={11}
        top={'50%'}
        style={{
          transform: [{ translateY: -20 }],
        }}>
        <Button bg="purple100" isFullWidth={false} onPress={() => router.push(SCREENS.MOOD_CALENDAR)}>
          <View fd="row" ai="center" gap={2}>
            <Text fs="14" fw="600" color="purple500">
              Summary
            </Text>
            <Feather name="chevron-right" size={16} color={colors.purple500} />
          </View>
        </Button>
      </View>
    </View>
  );
};

export interface ArticleData {
  image: ImageSourcePropType;
  title: string;
  content: string;
}

const ARTICLES: ArticleData[] = [
  {
    title: 'Uplift Your Spirits: Simple Ways to Brighten Your Day',
    image: require('@assets/placeholders/article-1.png'),
    content:
      "When life feels heavy, there are simple yet powerful ways to lift your spirits and bring more brightness into your day. Start with gentle self-care practices like taking a few deep breaths, stepping outside for fresh air, or treating yourself to your favorite comfort drink.\n\nConnect with others – whether it's sending a message to a friend, sharing a laugh with family, or simply exchanging smiles with strangers. Small acts of kindness, both to yourself and others, can create ripples of positivity.\n\nRemember to celebrate small wins and find joy in simple pleasures. Put on your favorite music, move your body, or engage in a hobby that brings you peace. Sometimes the smallest changes in our daily routine can make the biggest difference in how we feel.",
  },
  {
    title: 'In a Creative Slump? Try These Fun Activities',
    image: require('@assets/placeholders/article-2.png'),
    content:
      "Feeling stuck in a creative rut? Don't worry – it happens to everyone! The key is to shake things up and try something new. Start by exploring activities that spark joy and curiosity, even if they seem unrelated to your usual creative pursuits.\n\nTry your hand at coloring or doodling without judgment, take a nature walk with your camera, or create a playlist that matches your current mood. Sometimes, creativity flows best when we're not forcing it. Simple activities like rearranging your space, trying a new recipe, or writing a short story about your day can unlock fresh perspectives.\n\nRemember, creativity isn't about perfection – it's about expression and exploration. Dance like nobody's watching, write terrible poetry, or make something with your hands. These playful moments often lead to unexpected breakthroughs and renewed inspiration.",
  },
  {
    title: 'Embracing Every Emotion: Why Tracking Your Mood Matters',
    image: require('@assets/placeholders/article-3.png'),
    content:
      "Embracing every emotion is a vital part of personal growth and mental well-being. By tracking your moods, you gain valuable insights into your emotional patterns, triggers, and progress over time. This awareness helps you better understand yourself, manage stress, and celebrate your healing journey.\n\nMood tracking isn't just about noticing the tough days—it's about recognizing your resilience and the moments of joy, too. Whether you're feeling heartbroken, anxious, or optimistic, every emotion is valid and worth acknowledging.",
  },
];

export const MoodScreen = () => {
  return (
    <SafeAreaView style={{ flex: 1 }}>
      <ScrollView>
        <MoodHeartSlider />
        <Text fs="14" fw="600" color="neutral80" ml={16} mb={16}>
          Based on your mood lately
        </Text>
        <FlashList
          horizontal
          data={ARTICLES}
          renderItem={({ item }) => (
            <Pressable onPress={() => router.push(SCREENS.MOOD_ARTICLE, { image: item.image, title: item.title, content: item.content })}>
              <View w={122} h={141} br={12} bg="background" ml={16} bw={1} bc="neutral10">
                <Image
                  source={item.image}
                  height={97}
                  style={{
                    height: 120,
                    width: '100%',
                    borderTopLeftRadius: 12,
                    borderTopRightRadius: 12,
                  }}
                />
                <Text fs="10" fw="500" color="neutral80" mx={9} my={8} numberOfLines={3}>
                  {item.title}
                </Text>
              </View>
            </Pressable>
          )}
          estimatedItemSize={5}
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={{ paddingRight: 16, paddingBottom: 30 }}
        />
        <View h={15} />
      </ScrollView>
    </SafeAreaView>
  );
};
