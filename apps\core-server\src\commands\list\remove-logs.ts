import { Command } from "@/lib/command-module.js";
import db from "@repo/db";
import { moodTable } from "@repo/db/schema";
import { logger } from "@repo/lib";
import { sql } from "drizzle-orm";

const USER_ID = process.env.TEST_USER_ID;

export const removeLogs = new Command({
  name: "remove-logs",
  description: "Remove all logs of a particular user",
  fn: async () => {
    logger.info("Task Started");
    await deleteLogs();
    logger.info("Task Done");
  },
});

async function deleteLogs() {
  const output = await db
    .delete(moodTable)
    .where(sql`${moodTable.userId} = ${USER_ID}`);
  console.log(`Removed ${output.rowCount} log entries.`);
}
