import { Button, Text, TextInput, View } from '@components/native';
import { ScreenWrapper } from '@components/shared';
import { Switch } from '@components/shared/animated';
import { useTheme } from '@context/index';
import { Controller, useForm } from 'react-hook-form';
import { Image } from 'react-native';

export const CreatePost = () => {
  const { colors } = useTheme();
  const { control, setValue } = useForm({
    defaultValues: {
      title: '',
      description: '',
      disableComments: false,
    },
  });
  return (
    <ScreenWrapper
      title={
        <Text ff="PlayfairDisplay-SemiBold" fs="20" fw="600">
          New Post
        </Text>
      }>
      <View px={20} pt={26} flex={1} jc="space-between">
        <View>
          <View fd="row" ai="center" mb={31}>
            <Image style={{ width: 50, height: 50, borderWidth: 1, borderRadius: 36, marginRight: 11, borderColor: colors.neutral10 }} source={require('@assets/placeholders/avatar.png')} />
            <Text fw="600" fs="14" color="neutral80">
              @luca_b_2025
            </Text>
          </View>
          {/* Add minLength 10 */}
          <TextInput label="Add Title" labelType="background" maxLength={50} control={control} name="title" gapBottom={33} />
          {/* Add minLength 30 */}
          <TextInput label="Add Description" labelType="background" maxLength={1000} control={control} name="description" multiline={true} />
          <View fd="row" ai="center" jc="space-between" pt={25}>
            <Text fw="400" fs="12" color="neutral70">
              Disable Comments
            </Text>
            <Controller
              control={control}
              name="disableComments"
              render={({ field: { value, onChange } }) => (
                <Switch
                  isActive={value}
                  onToggle={newValue => {
                    onChange(newValue);
                    setValue('disableComments', newValue);
                  }}
                />
              )}
            />
          </View>
        </View>
        <Button mb={53} isFullWidth={true} onPress={() => {}}>
          POST
        </Button>
      </View>
    </ScreenWrapper>
  );
};
