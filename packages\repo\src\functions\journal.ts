import { journalCoversTable } from "@repo/db/schema";
import { RepoPayload } from "./types.js";
import db from "@repo/db";
import { eq } from "drizzle-orm";

type AddJournalCoverPayload = {
  name: string;
  url: string;
};
export class JournalRepo {
  static async addJournalCover(payload: RepoPayload<AddJournalCoverPayload[]>) {
    await payload.tx.insert(journalCoversTable).values(payload.data);
  }
  static async listJournalCovers() {
    return db
      .select({
        id: journalCoversTable.id,
        name: journalCoversTable.name,
        url: journalCoversTable.url
      })
      .from(journalCoversTable);
  }

  static async findCover(id: string) {
    const [cover] = await db
      .select({
        id: journalCoversTable.id,
        name: journalCoversTable.name,
        url: journalCoversTable.url
      })
      .from(journalCoversTable)
      .where(eq(journalCoversTable.id, id));
    return cover;
  }
}
