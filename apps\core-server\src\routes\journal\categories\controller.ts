import { bodyValidator, mediaBodyValidator } from "@/lib/validation-module.js";
import db from "@repo/db";
import { journalCategoryTable, journalTable } from "@repo/db/schema";
import { and, count, eq, isNull, or, sql } from "drizzle-orm";
import type { Request, Response, NextFunction } from "express";
import { createJournalCategoryValidator } from "./validator.js";
import { MediaModule } from "@repo/bin-packages";
import { JournalRepo } from "@repo/repo";
import { Infer } from "@vinejs/vine/types";

export class JournalCategoriesController {
  static async getAllCategories(
    req: Request,
    res: Response,
    next: NextFunction,
  ) {
    try {
      const rows = await db
        .select({
          id: journalCategoryTable.id,
          title: journalCategoryTable.title,
          type: journalCategoryTable.type,
          bgImage: journalCategoryTable.bgImage,
          totalCount: count(journalTable.id),
        })
        .from(journalCategoryTable)
        .leftJoin(
          journalTable,
          and(
            eq(journalTable.categoryId, journalCategoryTable.id),
            eq(journalTable.userId, req.user.id),
            isNull(journalTable.deletedAt),
          ),
        )
        .where(
          or(
            isNull(journalCategoryTable.userId),
            eq(journalCategoryTable.userId, req.user.id),
          ),
        )
        .groupBy(journalCategoryTable.id)
        .orderBy(sql`CASE WHEN "type" = 'all' THEN 1 ELSE 2 END`);

      const totalJournals = rows.reduce((acc, row) => {
        return acc + row.totalCount;
      }, 0);
      res.json({
        data: {
          data: rows.map((row) => ({
            ...row,
            totalCount: row.type == "all" ? totalJournals : row.totalCount,
          })),
        },
      });
    } catch (error) {
      next(error);
    }
  }
  @bodyValidator(createJournalCategoryValidator)
  static async addCategory(req: Request, res: Response, next: NextFunction) {
    try {
      const payload = req.body as Infer<typeof createJournalCategoryValidator>;

      const file = await JournalRepo.findCover(payload.coverId);

      if (!file) {
        throw new Error("Cover not found");
      }
      const [category] = await db
        .insert(journalCategoryTable)
        .values({
          userId: req.user.id,
          title: payload.title,
          type: payload.type,
          bgImage: file.url,
        })
        .returning();
      res.json({
        data: { data: category },
        message: "Category created successfully",
      });
    } catch (error) {
      next(error);
    }
  }
  static async getCovers(req: Request, res: Response, next: NextFunction) {
    try {
      const covers = await JournalRepo.listJournalCovers();
      res.json({ data: { data: covers } });
    } catch (error) {
      next(error);
    }
  }
}
